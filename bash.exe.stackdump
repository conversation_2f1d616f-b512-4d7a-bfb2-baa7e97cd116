Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDDBD10000 ntdll.dll
7FFDD0F00000 aswhook.dll
7FFDDA5E0000 KERNEL32.DLL
7FFDD9230000 KERNELBASE.dll
7FFDDB1A0000 USER32.dll
7FFDD8E00000 win32u.dll
7FFDDB9B0000 GDI32.dll
7FFDD8E30000 gdi32full.dll
7FFDD90D0000 msvcp_win.dll
7FFDD9800000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDDB7E0000 advapi32.dll
7FFDDBA60000 msvcrt.dll
7FFDDB0F0000 sechost.dll
7FFDD9610000 bcrypt.dll
7FFDDA6B0000 RPCRT4.dll
7FFDD85A0000 CRYPTBASE.DLL
7FFDD9780000 bcryptPrimitives.dll
7FFDDB700000 IMM32.DLL
