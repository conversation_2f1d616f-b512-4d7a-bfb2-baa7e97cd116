/* Reset and base styles */
* {
  box-sizing: border-box;
}

/* Override default Vite styles */
body {
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
  place-items: unset !important;
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
}

#root {
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
}

.app {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.chat-container {
  width: 100%;
  height: 100vh;
  background: white;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header */
.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.chat-header h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
}

.chat-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

/* Error banner */
.error-banner {
  background: #fee;
  color: #c33;
  padding: 12px 20px;
  border-bottom: 1px solid #fcc;
  font-size: 0.9rem;
}

/* Main content area */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

/* Sidebar */
.sidebar {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #e1e5e9;
  padding: 20px;
  overflow-y: auto;
}

.sidebar h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.1rem;
}

.agent-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.agent-item {
  padding: 12px 16px;
  margin: 8px 0;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #666;
}

.agent-item.active {
  border-color: #667eea;
  background: #f0f4ff;
  color: #667eea;
}

.agent-icon {
  font-size: 1.2rem;
}

.agent-description {
  font-size: 0.8rem;
  color: #888;
  margin-top: 4px;
}

/* Messages container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 30px 40px;
  background: #ffffff;
  width: 100%;
}

.welcome-message {
  text-align: center;
  color: #666;
  max-width: 800px;
  margin: 60px auto;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e1e5e9;
}

.welcome-message h3 {
  color: #333;
  margin-bottom: 16px;
}

.welcome-message ul {
  text-align: left;
  margin: 20px 0;
}

.welcome-message li {
  margin: 8px 0;
}

/* Messages */
.message {
  margin-bottom: 24px;
  max-width: 80%;
  animation: fadeIn 0.3s ease-in;
}

.message.user {
  margin-left: auto;
  margin-right: 0;
}

.message.agent {
  margin-right: auto;
  margin-left: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-sender {
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 6px;
}

.message.user .message-sender {
  text-align: right;
  color: #667eea;
  justify-content: flex-end;
}

/* Agent-specific styling */
.message-sender::before {
  content: "🤖";
  font-size: 0.9rem;
}

.message.user .message-sender::before {
  content: "👤";
}

/* Specific agent icons and colors */
.message-sender[data-agent="Course Advisor"]::before {
  content: "📚";
}

.message-sender[data-agent="Course Advisor"] {
  color: #28a745;
}

.message-sender[data-agent="University Poet"]::before {
  content: "🎭";
}

.message-sender[data-agent="University Poet"] {
  color: #6f42c1;
}

.message-sender[data-agent="Scheduling Assistant"]::before {
  content: "📅";
}

.message-sender[data-agent="Scheduling Assistant"] {
  color: #fd7e14;
}

.message-sender[data-agent="Triage Agent"]::before {
  content: "🎯";
}

.message-sender[data-agent="Triage Agent"] {
  color: #17a2b8;
}

.message-text {
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.6;
  word-wrap: break-word;
  white-space: pre-wrap; /* Preserve line breaks and spaces */
}

.message-text strong {
  font-weight: 700;
  color: inherit;
}

.message-text em {
  font-style: italic;
  color: inherit;
}

.message.user .message-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.agent .message-text {
  background: white;
  border: 1px solid #e1e5e9;
  border-bottom-left-radius: 4px;
  color: #333;
}

/* Loading animation */
.message-text.loading {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-indicator {
  color: #666;
}

.dots {
  animation: typing 1.5s infinite;
}

@keyframes typing {
  0%, 20% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

/* Input container */
.input-container {
  padding: 24px 40px;
  background: white;
  border-top: 1px solid #e1e5e9;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.input-wrapper {
  display: flex;
  gap: 16px;
  align-items: center;
  width: 100%;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 24px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s;
}

.message-input:focus {
  border-color: #667eea;
}

.message-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.send-button, .clear-button {
  padding: 12px 20px;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.clear-button {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.clear-button:hover:not(:disabled) {
  background: #e9ecef;
  transform: translateY(-1px);
}

.clear-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}



/* Responsive design */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid #e1e5e9;
  }

  .agent-list {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding-bottom: 8px;
  }

  .agent-item {
    flex-shrink: 0;
    min-width: 200px;
  }

  .messages-container {
    padding: 20px 30px;
  }

  .input-container {
    padding: 20px 30px;
  }
}

@media (max-width: 768px) {
  .app {
    padding: 0;
  }

  .chat-container {
    height: 100vh;
    border-radius: 0;
  }

  .chat-header h1 {
    font-size: 1.5rem;
  }

  .sidebar {
    padding: 16px;
  }

  .messages-container {
    padding: 16px 20px;
  }

  .message {
    max-width: 85%;
  }

  .input-container {
    padding: 16px 20px;
  }

  .input-wrapper {
    flex-direction: column;
    gap: 12px;
  }

  .message-input {
    width: 100%;
  }

  .send-button, .clear-button {
    width: 100%;
  }

  .welcome-message {
    margin: 20px auto;
    padding: 24px;
  }
}
