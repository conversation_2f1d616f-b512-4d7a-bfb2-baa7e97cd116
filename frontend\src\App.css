/* Reset and base styles */
* {
  box-sizing: border-box;
}

.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.chat-container {
  width: 100%;
  max-width: 800px;
  height: 90vh;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header */
.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.chat-header h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
}

.chat-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

/* Error banner */
.error-banner {
  background: #fee;
  color: #c33;
  padding: 12px 20px;
  border-bottom: 1px solid #fcc;
  font-size: 0.9rem;
}

/* Messages container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
}

.welcome-message {
  text-align: center;
  color: #666;
  max-width: 500px;
  margin: 40px auto;
}

.welcome-message h3 {
  color: #333;
  margin-bottom: 16px;
}

.welcome-message ul {
  text-align: left;
  margin: 20px 0;
}

.welcome-message li {
  margin: 8px 0;
}

/* Messages */
.message {
  margin-bottom: 20px;
  max-width: 80%;
}

.message.user {
  margin-left: auto;
}

.message.agent {
  margin-right: auto;
}

.message-sender {
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #666;
}

.message.user .message-sender {
  text-align: right;
  color: #667eea;
}

.message-text {
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.4;
  word-wrap: break-word;
}

.message.user .message-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.agent .message-text {
  background: white;
  border: 1px solid #e1e5e9;
  border-bottom-left-radius: 4px;
  color: #333;
}

/* Loading animation */
.message-text.loading {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-indicator {
  color: #666;
}

.dots {
  animation: typing 1.5s infinite;
}

@keyframes typing {
  0%, 20% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

/* Input container */
.input-container {
  padding: 20px;
  background: white;
  border-top: 1px solid #e1e5e9;
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 24px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s;
}

.message-input:focus {
  border-color: #667eea;
}

.message-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.send-button, .clear-button {
  padding: 12px 20px;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.clear-button {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e1e5e9;
}

.clear-button:hover:not(:disabled) {
  background: #e9ecef;
  transform: translateY(-1px);
}

.clear-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Footer */
.chat-footer {
  padding: 12px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e1e5e9;
  text-align: center;
}

.session-info {
  font-size: 0.8rem;
  color: #666;
}

.session-info code {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .app {
    padding: 10px;
  }
  
  .chat-container {
    height: 95vh;
    border-radius: 12px;
  }
  
  .chat-header h1 {
    font-size: 1.5rem;
  }
  
  .message {
    max-width: 90%;
  }
  
  .input-wrapper {
    flex-direction: column;
    gap: 8px;
  }
  
  .message-input {
    width: 100%;
  }
  
  .send-button, .clear-button {
    width: 100%;
  }
}
