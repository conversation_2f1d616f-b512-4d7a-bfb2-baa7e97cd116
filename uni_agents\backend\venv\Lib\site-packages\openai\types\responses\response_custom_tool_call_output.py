# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Optional
from typing_extensions import Literal

from ..._models import BaseModel

__all__ = ["ResponseCustomToolCallOutput"]


class ResponseCustomToolCallOutput(BaseModel):
    call_id: str
    """The call ID, used to map this custom tool call output to a custom tool call."""

    output: str
    """The output from the custom tool call generated by your code."""

    type: Literal["custom_tool_call_output"]
    """The type of the custom tool call output. Always `custom_tool_call_output`."""

    id: Optional[str] = None
    """The unique ID of the custom tool call output in the OpenAI platform."""
